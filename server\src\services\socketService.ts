// Socket.io Service - DEVELOPMENT_RULES.md Kural 11
// ✅ Socket.io event naming conventions and patterns

import { Server as SocketIOServer } from 'socket.io'
import { Server as HTTPServer } from 'http'
import jwt from 'jsonwebtoken'
import { logger } from '../utils/logger'
import { AppError, ErrorCodes } from '../utils/AppError'
// ✅ Import socket events and types
const SOCKET_EVENTS = {
  // Order events
  ORDER_CREATE: 'order:create',
  ORDER_UPDATE: 'order:update',
  ORDER_CREATED: 'order:created',
  ORDER_UPDATED: 'order:updated',

  // Kitchen events
  KITCHEN_ORDER_NEW: 'kitchen:order:new',
  KITCHEN_ORDER_READY: 'kitchen:order:ready',

  // Payment events
  PAYMENT_SUCCESS: 'payment:success',
  PAYMENT_FAILED: 'payment:failed',

  // Table events
  TABLE_STATUS_CHANGED: 'table:status:changed',

  // User events
  USER_CONNECTED: 'user:connected',
  USER_DISCONNECTED: 'user:disconnected',

  // System events
  SYSTEM_NOTIFICATION: 'system:notification',

  // Connection events
  HEARTBEAT: 'heartbeat'
} as const

const SOCKET_ROOMS = {
  COMPANY: (companyId: string) => `company:${companyId}`,
  BRANCH: (branchId: string) => `branch:${branchId}`,
  KITCHEN: (branchId: string) => `kitchen:${branchId}`,
  CASHIER: (branchId: string) => `cashier:${branchId}`,
  MANAGER: (branchId: string) => `manager:${branchId}`,
  WAITER: (branchId: string) => `waiter:${branchId}`,
  ORDERS: (branchId: string) => `orders:${branchId}`,
  TABLES: (branchId: string) => `tables:${branchId}`,
  USER: (userId: string) => `user:${userId}`
} as const

type SocketEvent = typeof SOCKET_EVENTS[keyof typeof SOCKET_EVENTS]

interface SocketUser {
  id: string
  username: string
  role: string
  companyId: string
  branchId: string
}

interface AuthenticatedSocket {
  user: SocketUser
  join: (room: string) => void
  leave: (room: string) => void
  emit: (event: SocketEvent, data: any) => void
  broadcast: {
    to: (room: string) => {
      emit: (event: SocketEvent, data: any) => void
    }
  }
}

interface OrderEventPayload {
  orderId: string
  tableId?: string
  status?: string
  timestamp: string
  userId: string
}

interface KitchenEventPayload {
  orderId: string
  items: Array<{
    productId: string
    quantity: number
    notes?: string
  }>
  estimatedTime?: number
  priority?: 'low' | 'normal' | 'high'
}

interface PaymentEventPayload {
  orderId: string
  amount: number
  method: 'cash' | 'card' | 'digital'
  status: 'pending' | 'success' | 'failed' | 'cancelled'
  transactionId?: string
}

interface TableEventPayload {
  tableId: string
  status: 'available' | 'occupied' | 'reserved' | 'cleaning'
  customerId?: string
  reservationTime?: string
}

interface SystemEventPayload {
  type: 'info' | 'warning' | 'error' | 'success'
  message: string
  timestamp: string
  userId?: string
  action?: string
}

export class SocketService {
  private io: SocketIOServer
  private connectedUsers = new Map<string, SocketUser>()

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.CLIENT_URL || 'http://localhost:5173',
        credentials: true
      },
      transports: ['websocket', 'polling']
    })

    this.setupMiddleware()
    this.setupEventHandlers()
    
    logger.info('Socket.io service initialized')
  }

  // ✅ Authentication middleware
  private setupMiddleware() {
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1]
        
        if (!token) {
          throw new AppError('Token required', 401, ErrorCodes.UNAUTHORIZED)
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
        const user: SocketUser = {
          id: decoded.userId,
          username: decoded.username,
          role: decoded.role,
          companyId: decoded.companyId,
          branchId: decoded.branchId
        }

        ;(socket as any).user = user
        this.connectedUsers.set(socket.id, user)

        logger.info('Socket authenticated', { 
          socketId: socket.id, 
          userId: user.id, 
          username: user.username 
        })

        next()
      } catch (error) {
        logger.error('Socket authentication failed', { error })
        next(new Error('Authentication failed'))
      }
    })
  }

  // ✅ Event handlers setup
  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      const user = (socket as any).user as SocketUser
      
      logger.info('User connected', { 
        socketId: socket.id, 
        userId: user.id,
        username: user.username,
        role: user.role
      })

      // ✅ Join user to appropriate rooms
      this.joinUserToRooms(socket as any, user)

      // ✅ Setup event listeners
      this.setupOrderEvents(socket as any)
      this.setupKitchenEvents(socket as any)
      this.setupPaymentEvents(socket as any)
      this.setupTableEvents(socket as any)
      this.setupSystemEvents(socket as any)

      // ✅ Handle disconnection
      socket.on('disconnect', () => {
        this.handleDisconnection(socket.id, user)
      })

      // ✅ Heartbeat
      socket.on(SOCKET_EVENTS.HEARTBEAT, () => {
        socket.emit(SOCKET_EVENTS.HEARTBEAT, { timestamp: new Date().toISOString() })
      })
    })
  }

  // ✅ Join user to appropriate rooms based on role
  private joinUserToRooms(socket: AuthenticatedSocket, user: SocketUser) {
    // Company and branch rooms
    socket.join(SOCKET_ROOMS.COMPANY(user.companyId))
    socket.join(SOCKET_ROOMS.BRANCH(user.branchId))
    socket.join(SOCKET_ROOMS.USER(user.id))

    // Role-based rooms
    switch (user.role.toLowerCase()) {
      case 'kitchen':
        socket.join(SOCKET_ROOMS.KITCHEN(user.branchId))
        break
      case 'cashier':
        socket.join(SOCKET_ROOMS.CASHIER(user.branchId))
        break
      case 'manager':
      case 'admin':
        socket.join(SOCKET_ROOMS.MANAGER(user.branchId))
        socket.join(SOCKET_ROOMS.KITCHEN(user.branchId))
        socket.join(SOCKET_ROOMS.CASHIER(user.branchId))
        break
      case 'waiter':
        socket.join(SOCKET_ROOMS.WAITER(user.branchId))
        break
    }

    // Feature rooms
    socket.join(SOCKET_ROOMS.ORDERS(user.branchId))
    socket.join(SOCKET_ROOMS.TABLES(user.branchId))

    logger.info('User joined rooms', { userId: user.id, role: user.role })
  }

  // ✅ Order events
  private setupOrderEvents(socket: AuthenticatedSocket) {
    socket.on(SOCKET_EVENTS.ORDER_CREATE, (data: OrderEventPayload) => {
      logger.info('Order create event', { userId: socket.user.id, orderId: data.orderId })
      
      // Broadcast to kitchen and managers
      socket.broadcast.to(SOCKET_ROOMS.KITCHEN(socket.user.branchId))
        .emit(SOCKET_EVENTS.KITCHEN_ORDER_NEW, data)
      
      // Broadcast to all order listeners
      socket.broadcast.to(SOCKET_ROOMS.ORDERS(socket.user.branchId))
        .emit(SOCKET_EVENTS.ORDER_CREATED, data)
    })

    socket.on(SOCKET_EVENTS.ORDER_UPDATE, (data: OrderEventPayload) => {
      logger.info('Order update event', { userId: socket.user.id, orderId: data.orderId })
      
      socket.broadcast.to(SOCKET_ROOMS.ORDERS(socket.user.branchId))
        .emit(SOCKET_EVENTS.ORDER_UPDATED, data)
    })
  }

  // ✅ Kitchen events
  private setupKitchenEvents(socket: AuthenticatedSocket) {
    socket.on(SOCKET_EVENTS.KITCHEN_ORDER_READY, (data: KitchenEventPayload) => {
      logger.info('Kitchen order ready', { userId: socket.user.id, orderId: data.orderId })
      
      // Notify waiters and managers
      socket.broadcast.to(SOCKET_ROOMS.WAITER(socket.user.branchId))
        .emit(SOCKET_EVENTS.KITCHEN_ORDER_READY, data)
      
      socket.broadcast.to(SOCKET_ROOMS.MANAGER(socket.user.branchId))
        .emit(SOCKET_EVENTS.KITCHEN_ORDER_READY, data)
    })
  }

  // ✅ Payment events
  private setupPaymentEvents(socket: AuthenticatedSocket) {
    socket.on(SOCKET_EVENTS.PAYMENT_SUCCESS, (data: PaymentEventPayload) => {
      logger.info('Payment success', { userId: socket.user.id, orderId: data.orderId })
      
      socket.broadcast.to(SOCKET_ROOMS.ORDERS(socket.user.branchId))
        .emit(SOCKET_EVENTS.PAYMENT_SUCCESS, data)
    })
  }

  // ✅ Table events
  private setupTableEvents(socket: AuthenticatedSocket) {
    socket.on(SOCKET_EVENTS.TABLE_STATUS_CHANGED, (data: TableEventPayload) => {
      logger.info('Table status changed', { userId: socket.user.id, tableId: data.tableId })
      
      socket.broadcast.to(SOCKET_ROOMS.TABLES(socket.user.branchId))
        .emit(SOCKET_EVENTS.TABLE_STATUS_CHANGED, data)
    })
  }

  // ✅ System events
  private setupSystemEvents(socket: AuthenticatedSocket) {
    // System events are usually server-initiated, so just logging here
    logger.info('System events setup completed for user', { userId: socket.user.id })
  }

  // ✅ Handle disconnection
  private handleDisconnection(socketId: string, user: SocketUser) {
    this.connectedUsers.delete(socketId)
    
    logger.info('User disconnected', { 
      socketId, 
      userId: user.id,
      username: user.username 
    })

    // Broadcast user disconnection to relevant rooms
    this.io.to(SOCKET_ROOMS.BRANCH(user.branchId))
      .emit(SOCKET_EVENTS.USER_DISCONNECTED, {
        userId: user.id,
        username: user.username,
        timestamp: new Date().toISOString()
      })
  }

  // ✅ Public methods for emitting events from services
  public emitToUser(userId: string, event: SocketEvent, data: any) {
    this.io.to(SOCKET_ROOMS.USER(userId)).emit(event, data)
  }

  public emitToBranch(branchId: string, event: SocketEvent, data: any) {
    this.io.to(SOCKET_ROOMS.BRANCH(branchId)).emit(event, data)
  }

  public emitToKitchen(branchId: string, event: SocketEvent, data: any) {
    this.io.to(SOCKET_ROOMS.KITCHEN(branchId)).emit(event, data)
  }

  public emitSystemNotification(branchId: string, notification: SystemEventPayload) {
    this.io.to(SOCKET_ROOMS.BRANCH(branchId))
      .emit(SOCKET_EVENTS.SYSTEM_NOTIFICATION, notification)
  }

  // ✅ Get connected users count
  public getConnectedUsersCount(): number {
    return this.connectedUsers.size
  }

  public getConnectedUsersByBranch(branchId: string): SocketUser[] {
    return Array.from(this.connectedUsers.values())
      .filter(user => user.branchId === branchId)
  }
}

// ✅ Singleton instance
let socketService: SocketService | null = null

export const initializeSocketService = (server: HTTPServer): SocketService => {
  if (!socketService) {
    socketService = new SocketService(server)
  }
  return socketService
}

export const getSocketService = (): SocketService => {
  if (!socketService) {
    throw new Error('Socket service not initialized')
  }
  return socketService
}
