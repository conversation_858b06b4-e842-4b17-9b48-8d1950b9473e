# 🏗️ Restoran POS - Geliştirm<PERSON> Kuralları

> **Bu dosya projenin kritik geliştirme kurallarını içerir. Sadece sık karşılaşılan sorunları çözen kurallar burada.**

## 📁 **1. KLASÖR YAPISI**

```
atropos/
├── desktop/src/
│   ├── components/  # React bileşenleri (PascalCase)
│   ├── pages/       # Sayfa bileşenleri
│   ├── hooks/       # Custom hooks (camelCase)
│   ├── services/    # API servisleri
│   ├── store/       # Zustand stores
│   └── i18n/        # Dil dosyaları
├── server/src/
│   ├── controllers/ # Route handlers
│   ├── routes/      # API routes
│   ├── services/    # İş mantığı
│   └── validators/  # Validation schemas
├── shared/src/types/ # Ortak tipler
└── prisma/          # Database schema
```

## 📋 **2. İSİMLENDİRME**

- **Dosyalar**: `PascalCase.tsx`, `camelCase.ts`
- **<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>**: `camelCase`
- **Sabitler**: `UPPER_SNAKE_CASE`
- **Database**: Tablolar `PascalCase`, alanlar `camelCase`

## 🌐 **3. API RESPONSE FORMAT**

```typescript
interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}
```

## 🌍 **4. i18n KULLANIMI**

```typescript
// Hiyerarşik yapı
const { t } = useTranslation()
<Button>{t('common.save')}</Button>
<Typography>{t('orders.newOrder')}</Typography>
```

## 🚨 **5. KRİTİK VALIDATION KURALLARI**

### ✅ **Query Validation - BUGÜN YAŞANAN SORUN!**
```typescript
// ❌ YANLIŞ - PrismaClientValidationError'a neden olur!
const query = req.query as ProductQueryInput // sortBy undefined olabilir!

// ✅ DOĞRU - validateQuery middleware kullan
export const validateQuery = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Parse ve default değerlerle validate et
      const validatedQuery = schema.parse(req.query)
      ;(req as any).validatedQuery = validatedQuery
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Query validation failed',
          details: error.issues
        })
      }
    }
  }
}

// Controller'da kullanım
const query = (req as any).validatedQuery as ProductQueryInput
```

### ✅ **Body Validation:**
```typescript
export const validateBody = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Body validation failed',
          details: error.issues
        })
      }
    }
  }
}
```

## 🔗 **6. PRISMA INCLUDE RELATIONS**

### ✅ **Include Pattern:**
```typescript
// ✅ Doğru include kullanımı
const products = await prisma.product.findMany({
  include: {
    category: true,
    tax: true,
    variants: {
      where: { deletedAt: null },
      orderBy: { displayOrder: 'asc' }
    }
  },
  orderBy: { [query.sortBy]: query.sortOrder }, // query.sortBy garantili
  skip: (query.page - 1) * query.limit,
  take: query.limit
})
```

### 🚨 **CRITICAL: Schema değişikliklerinden sonra:**
```bash
npx prisma generate  # MUTLAKA çalıştır!
```

## 🚨 **7. ERROR HANDLING**

```typescript
// ✅ AppError Class
class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public code: string
  ) {
    super(message)
  }
}

// ✅ Service'de kullanım
try {
  const result = await someOperation()
  return result
} catch (error) {
  logger.error('Operation failed:', error)
  throw new AppError('Operation failed', 500, 'OPERATION_FAILED')
}
```

## 🔐 **8. AUTHENTICATION**

```typescript
// ✅ Auth middleware
export const requireAuth = (roles?: UserRole[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const token = req.headers.authorization?.split(' ')[1]
      const user = await verifyToken(token)

      if (roles && !roles.includes(user.role)) {
        throw new AppError('Insufficient permissions', 403, 'FORBIDDEN')
      }

      req.user = user
      next()
    } catch (error) {
      next(error)
    }
  }
}
```

## 🗄️ **9. DATABASE TRANSACTIONS**

```typescript
// ✅ Prisma transaction pattern
const createOrderWithItems = async (orderData: CreateOrderInput) => {
  return await prisma.$transaction(async (tx) => {
    const order = await tx.order.create({ data: orderData })

    await tx.orderItem.createMany({
      data: orderData.items.map(item => ({
        ...item,
        orderId: order.id
      }))
    })

    return order
  })
}
```

## 📝 **10. LOGGING**

```typescript
// ✅ Winston logger
import winston from 'winston'

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.Console()
  ]
})

// Kullanım
logger.info('User logged in', { userId: user.id })
logger.error('Order creation failed', { error, orderData })
```

## 🔌 **11. SOCKET.IO EVENT NAMING**

```typescript
// ✅ Event naming convention
const SOCKET_EVENTS = {
  // Client -> Server
  ORDER_CREATE: 'order:create',
  ORDER_UPDATE: 'order:update',
  ORDER_DELETE: 'order:delete',

  // Server -> Client
  ORDER_CREATED: 'order:created',
  ORDER_UPDATED: 'order:updated',
  ORDER_DELETED: 'order:deleted',

  // Real-time updates
  KITCHEN_ORDER_NEW: 'kitchen:order:new',
  PAYMENT_STATUS_CHANGED: 'payment:status:changed'
} as const

// Kullanım
socket.emit(SOCKET_EVENTS.ORDER_CREATE, orderData)
socket.on(SOCKET_EVENTS.ORDER_CREATED, handleOrderCreated)
```

## 🌐 **12. ENVIRONMENT VARIABLES**

```typescript
// ✅ Environment variables typing
interface EnvironmentVariables {
  NODE_ENV: 'development' | 'production' | 'test'
  PORT: string
  DATABASE_URL: string
  JWT_SECRET: string
  LOG_LEVEL: 'error' | 'warn' | 'info' | 'debug'
}

// ✅ Validation
const env = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']),
  PORT: z.string().default('3000'),
  DATABASE_URL: z.string(),
  JWT_SECRET: z.string().min(32),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info')
}).parse(process.env)

export { env }
```

## 🔷 **13. TYPESCRIPT PATTERNS**

```typescript
// ✅ Strict type definitions
interface CreateOrderInput {
  tableId: string
  items: OrderItemInput[]
  customerId?: string
  notes?: string
}

// ✅ Utility types
type PartialOrder = Partial<Order>
type OrderWithItems = Order & { items: OrderItem[] }
type CreateOrderData = Omit<Order, 'id' | 'createdAt' | 'updatedAt'>

// ✅ Generic API response
type ApiResponse<T> = {
  success: true
  data: T
} | {
  success: false
  error: string
  code?: string
}
```

## 🧩 **14. COMPONENT STRUCTURE**

```typescript
// ✅ Component pattern
interface ComponentProps {
  // Props interface
}

export const Component: React.FC<ComponentProps> = ({
  prop1,
  prop2
}) => {
  // 1. Hooks
  const { t } = useTranslation()
  const [state, setState] = useState()

  // 2. Event handlers
  const handleClick = useCallback(() => {
    // Handler logic
  }, [dependencies])

  // 3. Effects
  useEffect(() => {
    // Effect logic
  }, [dependencies])

  // 4. Render
  return (
    <Box>
      {/* JSX */}
    </Box>
  )
}
```

## 🗃️ **15. STATE MANAGEMENT (Zustand)**

```typescript
// ✅ Store pattern
interface OrderStore {
  orders: Order[]
  currentOrder: Order | null
  loading: boolean

  // Actions
  fetchOrders: () => Promise<void>
  createOrder: (data: CreateOrderInput) => Promise<void>
  updateOrder: (id: string, data: Partial<Order>) => Promise<void>
  setCurrentOrder: (order: Order | null) => void
}

export const useOrderStore = create<OrderStore>((set, get) => ({
  orders: [],
  currentOrder: null,
  loading: false,

  fetchOrders: async () => {
    set({ loading: true })
    try {
      const orders = await orderService.getAll()
      set({ orders, loading: false })
    } catch (error) {
      set({ loading: false })
      throw error
    }
  },

  createOrder: async (data) => {
    const order = await orderService.create(data)
    set(state => ({ orders: [...state.orders, order] }))
  },

  setCurrentOrder: (order) => set({ currentOrder: order })
}))
```

## � **16. UI/UX PATTERNS**

```typescript
// ✅ Consistent spacing
const spacing = {
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px'
}

// ✅ Loading states
const [loading, setLoading] = useState(false)

return (
  <Button
    disabled={loading}
    startIcon={loading ? <CircularProgress size={16} /> : <SaveIcon />}
  >
    {loading ? t('common.saving') : t('common.save')}
  </Button>
)

// ✅ Error boundaries
<ErrorBoundary fallback={<ErrorFallback />}>
  <Component />
</ErrorBoundary>
```

## ⚡ **17. PERFORMANCE OPTIMIZATION**

```typescript
// ✅ React.memo for expensive components
export const ExpensiveComponent = React.memo<Props>(({ data }) => {
  return <ComplexRender data={data} />
}, (prevProps, nextProps) => {
  return prevProps.data.id === nextProps.data.id
})

// ✅ useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data)
}, [data])

// ✅ useCallback for event handlers
const handleClick = useCallback((id: string) => {
  onItemClick(id)
}, [onItemClick])

// ✅ Lazy loading
const LazyComponent = lazy(() => import('./LazyComponent'))
```

## �️ **18. ELECTRON OPTIMIZATION**

```typescript
// ✅ IPC communication
// Main process
ipcMain.handle('get-orders', async () => {
  return await orderService.getAll()
})

// Renderer process
const orders = await window.electronAPI.getOrders()

// ✅ Security
contextIsolation: true,
nodeIntegration: false,
sandbox: true

// ✅ Performance
webSecurity: false, // Only for local files
backgroundThrottling: false // For POS systems
```

## 🎨 **19. TEMA SİSTEMİ**

```typescript
// ✅ Semantic renkler kullan
<Button color="primary">Kaydet</Button>
<Chip color="success">Tamamlandı</Chip>
<Alert severity="error">Hata mesajı</Alert>

// ❌ Hard-coded renkler kullanma
<Button sx={{ backgroundColor: '#1976d2' }}>

// ✅ Tema sabitleri kullan
const theme = createTheme({
  palette: {
    primary: { main: '#1976d2' },
    secondary: { main: '#dc004e' },
    success: { main: '#2e7d32' },
    warning: { main: '#ed6c02' },
    error: { main: '#d32f2f' }
  },
  spacing: 8,
  shape: { borderRadius: 8 }
})

// ✅ Semantic color usage
colors.primary.main
semanticColors.order.pending
spacing.md
borderRadius.lg
```

## 🎯 **ÖZET - EN KRİTİK KURALLAR**

### 🚨 **1. Query Validation Sorunu:**
```typescript
// ❌ YANLIŞ: req.query direkt kullanma
const query = req.query as ProductQueryInput

// ✅ DOĞRU: validateQuery middleware kullan
const query = (req as any).validatedQuery as ProductQueryInput
```

### 🚨 **2. Prisma Generate:**
```bash
# Schema değişikliklerinden sonra MUTLAKA:
npx prisma generate
```

### 🚨 **3. Error Handling:**
```typescript
throw new AppError('Message', statusCode, 'ERROR_CODE')
```

### 🚨 **4. API Response:**
```typescript
{ success: boolean, data?: T, message?: string, error?: string }
```

### 🚨 **5. Tema Sistemi:**
```typescript
// Material-UI semantic colors kullan
<Button color="primary" />
<Alert severity="error" />
```

**Bu 5 kural %90 sorunları çözer! 🎯**



